<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 运维助手 - WMS 智能客服</title>
    <link id="theme-css" rel="stylesheet" href="./styles/theme-light.css" />
    <link rel="stylesheet" href="./styles/base.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* 现代化 AI 助手界面设计（绿主题 + 无边距 + 输入建议） */
        :root {
            /* Zoomlion/中联重科风格绿色搭配 */
            --primary: #A5CD39;        /* 主绿 */
            --primary-dark: #39B54A;   /* 深绿/渐变尾色 */
            --accent: #1E7F34;         /* 强调绿 */
            --bg: #F5F7F2;             /* 背景近白绿 */
            --text: #2d3748;           /* 正文色 */
            --muted: #718096;          /* 次级文字 */
        }
        
        /* 基础样式 */
        /* 说明：整体去除紫色背景与外间距，使用绿色系与全出血布局 */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        body.loaded {
            opacity: 1;
        }

        .app-container {
            display: flex;
            height: 100vh;
            background: white;
        }

        /* 左侧边栏常见问题：常态隐藏（改为输入建议形式） */
        .sidebar { display: none; }

        .sidebar-item {
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            color: #4a5568;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .sidebar-item:hover {
            background: rgba(165, 205, 57, 0.15);
            color: var(--primary-dark);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: #fff;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin: 0;                 /* 去除外边距，页面不留边 */
            background: #fff;
            border-radius: 0;          /* 直角 */
            box-shadow: none;          /* 无投影 */
            overflow: hidden;
            border: none;
        }

        .chat-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            padding: 12px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            min-height: 56px;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .ai-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .header-info h1 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: white;
            line-height: 1.2;
        }

        .header-info p {
            margin: 2px 0 0 0;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
        }

        .chat-status {
            display: flex;
            align-items: center;
            gap: 6px;
            background: rgba(255, 255, 255, 0.15);
            padding: 4px 12px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #10b981; /* 在线绿，与主题相近 */
            animation: pulse 2s infinite;
        }

        .status-text {
            color: white;
            font-size: 11px;
            font-weight: 500;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 12px;
            background: #f8fafc;
            position: relative;
        }

        .message {
            display: flex;
            gap: 10px;
            animation: messageSlide 0.3s ease-out;
            align-items: flex-start;
        }

        .message.user {
            flex-direction: row-reverse;
            margin-left: 8px; /* 缩小空白 */
        }

        .message.ai {
            margin-right: 8px; /* 缩小空白 */
        }

        .message-avatar {
            width: 28px;
            height: 28px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            flex-shrink: 0;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .message.ai .message-avatar {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            color: white;
        }

        .message-content {
            flex: 1;
            padding: 10px 14px;
            border-radius: 12px;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            position: relative;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
            width: fit-content;
            max-width: 70%;
        }

        .message.ai .message-content {
            background: white;
            color: #2d3748;
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-left: 4px solid var(--primary);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .message.user .message-content {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: 0 2px 10px rgba(57,181,74,0.25);
        }

        .message-time {
            font-size: 10px;
            color: rgba(0, 0, 0, 0.4);
            margin-top: 4px;
            text-align: right;
        }

        .message.user .message-time {
            color: rgba(255, 255, 255, 0.7);
            text-align: left;
        }

        .typing-indicator {
            display: flex;
            gap: 4px;
            padding: 12px 0;
            align-items: center;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #cbd5e0;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        .quick-actions {
            padding: 12px 16px;
            background: white;
            border-top: 1px solid rgba(0, 0, 0, 0.06);
        }

        .quick-actions-title {
            font-size: 12px;
            color: #4a5568;
            margin-bottom: 8px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .quick-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 8px;
        }

        .quick-btn {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border: 1px solid rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
            color: #2d3748;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            text-align: left;
        }

        .quick-btn:hover {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            border-color: transparent;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(57, 181, 74, 0.3);
        }

        .chat-input-area {
            padding: 12px 16px 16px 16px;
            background: white;
            border-top: 1px solid rgba(0, 0, 0, 0.06);
        }

        .input-wrapper {
            display: flex;
            gap: 8px;
            align-items: center; /* 统一垂直居中 */
            background: #f8fafc;
            border-radius: 16px;
            padding: 6px;
            border: 2px solid transparent;
            transition: all 0.2s ease;
            position: relative; /* 用于承载建议下拉 */
        }

        .input-wrapper:focus-within {
            border-color: var(--primary);
            background: white;
            box-shadow: 0 0 0 3px rgba(165, 205, 57, 0.12);
        }

        .input-field {
            flex: 1;
            min-height: 48px;
            max-height: 120px;
            padding: 12px 20px;
            border: none;
            background: transparent;
            color: #2d3748;
            resize: none;
            font-size: 15px;
            line-height: 1.5;
            outline: none;
            font-family: inherit;
        }

        .input-field::placeholder {
            color: #a0aec0;
        }

        .send-btn {
            width: 48px;
            height: 48px;
            border-radius: 16px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 4px 12px rgba(57, 181, 74, 0.3);
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(57, 181, 74, 0.45);
        }

        .send-btn:active {
            transform: translateY(0);
        }

        /* 输入框建议下拉（替代左侧常见问题/底部快捷区，聚焦时出现） */
        .suggestion-dropdown {
            position: absolute;
            left: 8px;
            right: 62px; /* 预留发送按钮 */
            bottom: calc(100% + 8px); /* 始终在输入框上方 */
            transform: none;
            background: #fff;
            border: 1px solid rgba(0,0,0,0.08);
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.08);
            padding: 6px;
            display: none; /* 默认隐藏 */
            z-index: 50;
            max-height: 220px;
            overflow: auto;
        }
        .suggestion-title {
            font-size: 12px;
            color: #4a5568;
            padding: 6px 8px;
            font-weight: 600;
        }
        .suggestion-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 10px;
            border-radius: 8px;
            cursor: pointer;
            color: var(--text);
            font-size: 13px;
        }
        .suggestion-item:hover, .suggestion-item.active {
            background: rgba(165, 205, 57, 0.12);
            color: var(--accent);
        }
        .suggestion-empty {
            padding: 10px;
            color: var(--muted);
            font-size: 12px;
        }

        .send-btn:disabled {
            background: #e2e8f0;
            color: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 常见问题小按钮（展开/收起建议） */
        .faq-chip {
            height: 40px; /* 与输入/发送更接近，视觉居中 */
            border-radius: 10px;
            border: 1px solid rgba(0,0,0,0.08);
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: #fff;
            padding: 0 12px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            transition: transform .15s ease, box-shadow .2s ease;
            box-shadow: 0 2px 8px rgba(57, 181, 74, 0.25);
            user-select: none;
        }
        .faq-chip:hover { transform: translateY(-1px); }
        .faq-chip:active { transform: translateY(0); }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #4a5568;
        }

        .welcome-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .welcome-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #2d3748;
        }

        .welcome-subtitle {
            font-size: 13px;
            color: #718096;
            margin-bottom: 16px;
        }

        .welcome-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 8px;
            margin-top: 12px;
        }

        .feature-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.06);
            text-align: left;
        }

        .feature-icon {
            font-size: 16px;
            margin-bottom: 4px;
        }

        .feature-title {
            font-weight: 600;
            margin-bottom: 2px;
            color: #2d3748;
            font-size: 12px;
        }

        .feature-desc {
            font-size: 11px;
            color: #718096;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-8px);
                opacity: 1;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                display: none;
            }

            .app-container {
                background: white;
            }

            .chat-container {
                margin: 0;
                border-radius: 0;
                height: 100vh;
            }

            .chat-header {
                padding: 12px 16px;
                border-radius: 0;
            }

            .header-left {
                gap: 8px;
            }

            .ai-avatar {
                width: 28px;
                height: 28px;
            }

            .header-info h1 {
                font-size: 14px;
            }

            .chat-messages {
                padding: 20px 12px;
            }

            .message.user {
                margin-left: 8px;
            }

            .message.ai {
                margin-right: 8px;
            }
            .message-content { max-width: 90%; }

            .quick-actions {
                padding: 20px 16px;
            }

            .quick-buttons {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .chat-input-area {
                padding: 16px;
            }

            .input-wrapper {
                gap: 12px;
            }
        }

        @media (max-width: 480px) {
            .header-info h1 {
                font-size: 18px;
            }

            .header-info p {
                font-size: 12px;
            }

            .chat-status {
                padding: 6px 12px;
            }

            .status-text {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-item active">
                🏠 常见问题快速解决
            </div>
            <div class="sidebar-item">
                🔍 二维码扫描问题
            </div>
            <div class="sidebar-item">
                📦 入库订单问题
            </div>
            <div class="sidebar-item">
                📤 出库库存问题
            </div>
            <div class="sidebar-item">
                📅 账期错误问题
            </div>
            <div class="sidebar-item">
                🚫 库存移动限制
            </div>
            <div class="sidebar-item">
                ⏰ 提前过账操作
            </div>
        </div>

        <div class="chat-container">
            <!-- 现代化头部 -->
            <div class="chat-header">
                <div class="header-left">
                    <div class="ai-avatar">🤖</div>
                    <div class="header-info">
                        <h1>AI 运维助手</h1>
                        <p>WMS 仓库管理系统智能客服</p>
                    </div>
                </div>
                <div class="chat-status">
                    <span class="status-dot"></span>
                    <span id="status-text" class="status-text">在线服务</span>
                </div>
            </div>

            <!-- 消息区域 -->
            <div class="chat-messages" id="chat-messages">
                <!-- 欢迎界面 -->
                <div class="welcome-message" id="welcome-screen">
                    <div class="welcome-icon">👋</div>
                    <div class="welcome-title">欢迎使用 AI 运维助手</div>
                    <div class="welcome-subtitle">专业解决 WMS 仓库管理系统问题</div>

                    <div class="welcome-features">
                        <div class="feature-item">
                            <div class="feature-icon">🔍</div>
                            <div class="feature-title">智能诊断</div>
                            <div class="feature-desc">快速识别并解决系统问题</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">⚡</div>
                            <div class="feature-title">即时响应</div>
                            <div class="feature-desc">7x24小时在线技术支持</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">📚</div>
                            <div class="feature-title">专业知识库</div>
                            <div class="feature-desc">基于丰富的运维经验</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快捷问题按钮（常态隐藏，聚焦输入时用上方建议下拉替代） -->
            <div class="quick-actions" style="display:none;">
                <div class="quick-actions-title">
                    💡 常见问题快速解决
                </div>
                <div class="quick-buttons">
                    <button class="quick-btn" data-question="二维码扫描不出来怎么办？">
                        🔍 二维码扫描问题
                    </button>
                    <button class="quick-btn" data-question="无法入库，系统提示找不到订单">
                        📦 入库订单问题
                    </button>
                    <button class="quick-btn" data-question="出库时提示库存冻结怎么处理？">
                        📤 出库库存问题
                    </button>
                    <button class="quick-btn" data-question="系统报错提示账期不对">
                        📅 账期错误问题
                    </button>
                    <button class="quick-btn" data-question="库存显示不适合移动">
                        🚫 库存移动限制
                    </button>
                    <button class="quick-btn" data-question="需要提前入库过账怎么操作？">
                        ⏰ 提前过账操作
                    </button>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input-area">
                <div class="input-wrapper">
                    <button id="faq-chip" type="button" class="faq-chip" title="常见问题">
                        💡 常见问题
                    </button>
                    <!-- 输入建议下拉 -->
                    <div id="suggestion-dropdown" class="suggestion-dropdown">
                        <div class="suggestion-title">常见问题（可点击快速填充）</div>
                        <div id="suggestion-list"></div>
                    </div>
                    <textarea
                        id="user-input"
                        class="input-field"
                        placeholder="描述您遇到的问题，我来为您提供专业解决方案..."
                        rows="1"
                    ></textarea>
                    <button id="send-btn" class="send-btn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M22 2L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="./ai.js"></script>
    <script>
        // 初始化 AI 配置
        (async () => {
            try {
                await AI.loadConfig('./ai-config.json');
                console.log('[AI] 运维助手配置加载成功');
            } catch (e) {
                // 使用内置配置兜底
                await AI.loadConfig({
                    mode: 'direct',
                    baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
                    apiKey: 'f1510fc9-df05-4cc7-b21f-85f7249800b6',
                    model: 'doubao-seed-1-6-flash-250715',
                    timeout: 30000,
                    headers: {}
                });
                console.log('[AI] 使用内置配置');
            }
        })();

        // DOM 元素
        const chatMessages = document.getElementById('chat-messages');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');
        const statusText = document.getElementById('status-text');
    const quickButtons = document.querySelectorAll('.quick-btn');
        const welcomeScreen = document.getElementById('welcome-screen');
    const suggestionDropdown = document.getElementById('suggestion-dropdown');
    const suggestionList = document.getElementById('suggestion-list');
    const faqChip = document.getElementById('faq-chip');

        // 运维知识库系统提示词
        const SYSTEM_PROMPT = `你是一个专业的 WMS (仓库管理系统) 运维助手。你的主要职责是帮助用户解决仓库操作中的各种问题。

请根据以下知识库回答用户问题：

## 二维码相关问题
1. **车身无码**: 在系统中手动输入车身铭牌上的序列号
2. **二维码与铭牌不一致**: 以铭牌序列号为准手动输入，后续在OA系统提交"电子码补码修正"申请
3. **需要提前入库过账**: 联系上游供应商，在"直接交货确认"环节走"港口交货"流程

## 入库问题
1. **查询不到入库订单**: 检查是否属于"港口交货"，如果是则在LTC系统完成收货
2. **过账异常，提示非账期月**: 联系公司财务部门处理

## 出库问题
1. **S4状态为"冻结"**: 先完成该物料的"收货入库"动作，或检查并解除冻结状态
2. **提示库存短缺**: 同上，检查库存状态和入库情况
3. **账期错误**: 修改出库凭证的OB日期，使其与当前账期匹配
4. **库存不适合移动**: 
   - 入库环节报错：联系 MM (物料管理) 模块顾问
   - 出库环节报错：联系 SD (销售与分销) 模块顾问

请用简洁、专业的语言回答，提供具体的操作步骤。如果问题超出知识库范围，建议联系相关技术支持。`;

        // 隐藏欢迎界面
        function hideWelcomeScreen() {
            if (welcomeScreen) {
                welcomeScreen.style.display = 'none';
            }
        }

        // 添加消息
        function addMessage(content, type = 'ai', isTyping = false) {
            // 首次发送消息时隐藏欢迎界面
            hideWelcomeScreen();

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = type === 'ai' ? '🤖' : '👤';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            if (isTyping) {
                contentDiv.innerHTML = `
                    <div class="typing-indicator">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                `;
            } else {
                contentDiv.innerHTML = content.replace(/\n/g, '<br>');

                // 添加时间戳
                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                timeDiv.textContent = new Date().toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                contentDiv.appendChild(timeDiv);
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);

            // 平滑滚动到底部
            setTimeout(() => {
                chatMessages.scrollTo({
                    top: chatMessages.scrollHeight,
                    behavior: 'smooth'
                });
            }, 100);

            return messageDiv;
        }

        // 发送消息到 AI
        async function sendToAI(userMessage) {
            try {
                statusText.textContent = '分析中...';

                const response = await AI.chatCompletion({
                    messages: [
                        { role: 'system', content: SYSTEM_PROMPT },
                        { role: 'user', content: userMessage }
                    ],
                    temperature: 0.3  // 降低随机性，提高回答的准确性
                });

                statusText.textContent = '在线服务';

                if (response.choices && response.choices[0] && response.choices[0].message) {
                    return response.choices[0].message.content;
                } else {
                    throw new Error('AI响应格式异常');
                }
            } catch (error) {
                statusText.textContent = '连接异常';
                console.error('AI调用失败:', error);

                // 降级到本地知识库
                return getFallbackResponse(userMessage);
            }
        }

        // 本地知识库兜底
        function getFallbackResponse(message) {
            const msg = message.toLowerCase();

            if (msg.includes('二维码') || msg.includes('扫码') || msg.includes('无码')) {
                return `🔍 <strong>二维码问题解决方案</strong>

<strong>🚗 车身无码情况</strong>
• 在系统中手动输入车身铭牌上的序列号完成操作

<strong>⚠️ 二维码与铭牌不一致</strong>
• 当前操作以铭牌序列号为准，手动输入完成
• 后续在OA系统提交"电子码补码修正"申请

<strong>💡 温馨提示</strong>
如需更详细的操作指导，请告诉我具体遇到的情况。`;
            }

            if (msg.includes('入库') && (msg.includes('订单') || msg.includes('找不到'))) {
                return `📦 <strong>入库订单问题解决方案</strong>

<strong>1️⃣ 查询不到入库订单</strong>
• 首先检查此订单是否属于"港口交货"
• 如果是，则无需在此处收货，请在LTC系统完成收货

<strong>2️⃣ 过账异常，提示非账期月</strong>
• 请联系公司财务部门进行处理

<strong>🤔 请告诉我</strong>
您遇到的是哪种具体情况？我可以提供更精准的解决方案。`;
            }

            if (msg.includes('出库') && (msg.includes('冻结') || msg.includes('库存'))) {
                return `📤 <strong>出库相关问题解决方案</strong>

<strong>❄️ 库存冻结/短缺</strong>
• 请仓管员先在系统中完成该物料的"收货入库"动作
• 或检查并解除冻结状态

<strong>📅 账期错误</strong>
• 修改出库凭证的OB日期，使其与当前账期匹配

<strong>🚫 库存不适合移动</strong>
• 入库环节报错：联系 MM (物料管理) 模块顾问
• 出库环节报错：联系 SD (销售与分销) 模块顾问

<strong>💬 需要帮助？</strong>
请告诉我需要详细说明哪个步骤的操作流程。`;
            }

            return `😊 <strong>很抱歉，我需要更多信息来帮助您</strong>

<strong>🔄 建议您可以：</strong>
1. 重新描述问题，包含更多关键词
2. 使用上方的快捷问题按钮
3. 联系技术支持获得专业帮助

<strong>💡 或者直接告诉我：</strong>
• 您在操作哪个功能模块？
• 具体的错误提示是什么？
• 问题出现的操作步骤？

我会根据您的描述提供更精准的解决方案！`;
        }

        // 处理发送消息
        async function handleSend() {
            const message = userInput.value.trim();
            if (!message) return;
            
            // 显示用户消息
            addMessage(message, 'user');
            userInput.value = '';
            sendBtn.disabled = true;
            
            // 显示输入指示器
            const typingMsg = addMessage('', 'ai', true);
            
            // 获取AI回复
            const aiResponse = await sendToAI(message);
            
            // 移除输入指示器，显示AI回复
            chatMessages.removeChild(typingMsg);
            addMessage(aiResponse, 'ai');
            
            sendBtn.disabled = false;
            userInput.focus();
            hideSuggestions();
        }

        // 事件监听
        sendBtn.addEventListener('click', handleSend);
        
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSend();
            }
        });

        // 自动调整输入框高度
        userInput.addEventListener('input', () => {
            userInput.style.height = 'auto';
            userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
        });

        // 快捷问题按钮
        quickButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const question = btn.dataset.question;
                userInput.value = question;
                handleSend();
            });
        });

        // ===== 输入建议（替代左侧常见问题 + 底部常见问题） =====
        const FAQ_SUGGESTIONS = [
            { icon: '🔍', text: '二维码扫描不出来怎么办？' },
            { icon: '📦', text: '无法入库，系统提示找不到订单' },
            { icon: '📤', text: '出库时提示库存冻结怎么处理？' },
            { icon: '📅', text: '系统报错提示账期不对' },
            { icon: '🚫', text: '库存显示不适合移动' },
            { icon: '⏰', text: '需要提前入库过账怎么操作？' }
        ];

        function renderSuggestions(filterText = '') {
            const ft = filterText.trim().toLowerCase();
            const items = FAQ_SUGGESTIONS.filter(i => i.text.toLowerCase().includes(ft));
            suggestionList.innerHTML = items.length
                ? items.map((i, idx) => `<div class="suggestion-item" data-index="${idx}" data-text="${i.text}">${i.icon} ${i.text}</div>`).join('')
                : `<div class="suggestion-empty">没有匹配的问题，直接回车发送您的问题</div>`;

            const els = suggestionList.querySelectorAll('.suggestion-item');
            els.forEach(el => {
                // 用 mousedown 提前触发，避免 input 先 blur 导致下拉被隐藏
                el.addEventListener('mousedown', (e) => {
                    e.preventDefault();
                    if (blurHideTimer) { clearTimeout(blurHideTimer); }
                    userInput.value = el.dataset.text;
                    // 直接发送
                    hideSuggestions(); // 立刻收起，避免挡住回复
                    handleSend();
                });
            });
        }

        function showSuggestions() {
            renderSuggestions(userInput.value);
            suggestionDropdown.style.display = 'block';
        }

        function hideSuggestions() {
            suggestionDropdown.style.display = 'none';
        }

        let blurHideTimer = null;
        // 仅在点击“常见问题”按钮时显示，不再因输入框焦点/输入而显示
        userInput.addEventListener('blur', () => {
            // 延迟隐藏，允许点击建议项
            blurHideTimer = setTimeout(hideSuggestions, 150);
        });
        suggestionDropdown.addEventListener('mousedown', () => {
            // 阻止 blur 隐藏
            if (blurHideTimer) { clearTimeout(blurHideTimer); }
        });
        suggestionDropdown.addEventListener('mouseup', () => {
            userInput.focus();
        });

        // 键盘导航：上下选、回车确认
        let activeIndex = -1;
        function moveActive(delta) {
            const nodes = [...suggestionList.querySelectorAll('.suggestion-item')];
            if (!nodes.length) return;
            activeIndex = (activeIndex + delta + nodes.length) % nodes.length;
            nodes.forEach((n, i) => n.classList.toggle('active', i === activeIndex));
            nodes[activeIndex].scrollIntoView({ block: 'nearest' });
        }
        userInput.addEventListener('keydown', (e) => {
            if (suggestionDropdown.style.display !== 'block') return;
            const nodes = [...suggestionList.querySelectorAll('.suggestion-item')];
            if (!nodes.length) return;
            if (e.key === 'ArrowDown') { e.preventDefault(); moveActive(1); }
            else if (e.key === 'ArrowUp') { e.preventDefault(); moveActive(-1); }
            else if (e.key === 'Enter' && activeIndex >= 0) {
                e.preventDefault();
                userInput.value = nodes[activeIndex].dataset.text;
                handleSend();
            }
        });

        // 点击“常见问题”小按钮展开/收起
        faqChip.addEventListener('click', (e) => {
            e.preventDefault();
            if (suggestionDropdown.style.display === 'block') {
                hideSuggestions();
            } else {
                showSuggestions();
                userInput.focus();
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 添加页面加载动画
            setTimeout(() => {
                document.body.classList.add('loaded');
            }, 100);

            // 添加一些交互效果
            const quickBtns = document.querySelectorAll('.quick-btn');
            quickBtns.forEach(btn => {
                btn.addEventListener('mouseenter', () => {
                    btn.style.transform = 'translateY(-2px) scale(1.02)';
                });
                btn.addEventListener('mouseleave', () => {
                    btn.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>